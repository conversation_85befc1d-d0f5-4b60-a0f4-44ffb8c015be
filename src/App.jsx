import "./App.css";
import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./i18n";
import Layout from "./Layout";
import ResourcePreloader from "./components/ResourcePreloader";

// Critical components
import Main_section from "./components/main_herosection/Main_section";
import ManagementFeatures from "./components/ManagementFeatures/ManagementFeatures";

// All other components (formerly lazy-loaded)
import SocialMediaIcons from "./components/social_media/SocialMediaIcons";
import InfoCard from "./components/InfoCard/InfoCard";
import About_Us from "./components/AboutUs/index";
import Thoughts from "./components/Thoughts/Thoughts";
import Testimonials from "./components/Testimonials/Testimonials";
import TextReveal from "./components/TextReveal/TextReveal";
import FAQ from "./components/FAQ/FAQ";
import About_Us_Hero from "./components/AboutUs/AboutUsHero";
import Delete from "./components/Delete";
import Terms from "./components/Terms/Terms";
import ContactUs from "./components/Contactus/ContactUs";
import Policy from "./components/Privacy_Policy/Policy";
import RedirectingLinks from "./components/RedirectingLinks/RedirectingLinks";
import ShareProfile from "./components/ShareProfile/ShareProfile";
import PDFViewer from "./components/vishal/vishal";
import MilanCard from "./components/milan/milan";
import SharePost from "./components/ShareProfile/SharePost";
import Features from "./components/Features/Features";
import Blogs from "./components/Blogs/index";
import SingleBlogView from "./components/Blogs/SingleBlogView";

function App() {
  return (
    <Router>
      <ResourcePreloader />
      <Routes>
        {/* Routes inside Layout (Navbar and Footer included) */}
        <Route element={<Layout />}>
          <Route
            path="/"
            element={
              <>
                <Main_section />
                <ManagementFeatures />
                <SocialMediaIcons />
                <InfoCard />
                <About_Us_Hero />
                <Thoughts />
                <Testimonials />
                <TextReveal />
                <FAQ />
              </>
            }
          />
          <Route path="/aboutus" element={<About_Us />} />
          <Route path="/solutions" element={<Features />} />
          <Route path="/blogs" element={<Blogs />} />
          <Route path="/blog/:slug" element={<SingleBlogView />} />
          <Route path="/testimonials" element={<Testimonials />} />
          <Route path="/delete" element={<Delete />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/privacy-policy" element={<Policy />} />
          <Route path="/contact-us" element={<ContactUs />} />
        </Route>

        <Route path="/app" element={<RedirectingLinks />} />
        <Route path="/share-profile" element={<ShareProfile />} />
        <Route
          path="/employees/FR-00103_vishal-kathiriya"
          element={<PDFViewer />}
        />
        <Route
          path="/employees/FR-3489720_milan-katrodiya"
          element={<MilanCard />}
        />
        <Route path="/get-post" element={<SharePost />} />
      </Routes>
    </Router>
  );
}

export default App;
