import { useEffect, useRef } from 'react';

/**
 * Custom hook to prevent aggressive unmounting on mobile devices
 * This hook helps components stay mounted when scrolling out of view
 * 
 * @param {string} componentName - Unique identifier for the component
 * @param {Object} options - Configuration options
 * @returns {Object} - Mount status and utilities
 */
export const usePersistentMount = (componentName, options = {}) => {
  const {
    enableLogging = false,
    preventUnmount = true,
    persistState = true
  } = options;

  const isMountedRef = useRef(false);
  const mountCountRef = useRef(0);
  const componentIdRef = useRef(componentName);

  useEffect(() => {
    // Track mount/unmount cycles
    mountCountRef.current++;
    isMountedRef.current = true;

    if (enableLogging) {
      console.log(`[${componentName}] Mounted (count: ${mountCountRef.current})`);
    }

    // Apply mobile-specific optimizations
    if (preventUnmount && typeof window !== 'undefined') {
      // Add CSS to prevent aggressive unmounting
      const style = document.createElement('style');
      style.textContent = `
        [data-component="${componentName}"] {
          contain: layout style paint;
          will-change: auto;
          transform: translateZ(0);
          backface-visibility: hidden;
        }
      `;
      document.head.appendChild(style);

      // Cleanup function
      return () => {
        isMountedRef.current = false;
        if (enableLogging) {
          console.log(`[${componentName}] Unmounted`);
        }
        
        // Remove the style element
        if (style.parentNode) {
          document.head.removeChild(style);
        }
      };
    }

    return () => {
      isMountedRef.current = false;
      if (enableLogging) {
        console.log(`[${componentName}] Unmounted`);
      }
    };
  }, [componentName, enableLogging, preventUnmount]);

  return {
    isMounted: isMountedRef.current,
    mountCount: mountCountRef.current,
    componentId: componentIdRef.current,
    // Helper function to get component data attributes
    getDataAttributes: () => ({
      'data-component': componentName,
      'data-mount-count': mountCountRef.current,
      'data-persistent': preventUnmount ? 'true' : 'false'
    })
  };
};

/**
 * Hook specifically for mobile optimization
 * Detects mobile devices and applies appropriate optimizations
 */
export const useMobileOptimization = () => {
  const isMobileRef = useRef(false);

  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
      const isSmallScreen = window.innerWidth <= 768;
      
      isMobileRef.current = isMobile || isSmallScreen;
      
      // Apply mobile-specific optimizations
      if (isMobileRef.current) {
        // Reduce animation complexity on mobile
        document.documentElement.style.setProperty('--mobile-optimized', '1');
        
        // Add mobile-specific CSS
        if (!document.querySelector('#mobile-optimization-styles')) {
          const style = document.createElement('style');
          style.id = 'mobile-optimization-styles';
          style.textContent = `
            @media (max-width: 768px) {
              * {
                /* Reduce repaints and reflows */
                backface-visibility: hidden;
                perspective: 1000px;
              }
              
              /* Optimize animations for mobile */
              .animate-scroll-left,
              .animate-scroll-right,
              .animate-scroll-up,
              .animate-scroll-down {
                transform: translateZ(0);
                will-change: transform;
              }
              
              /* Prevent aggressive unmounting */
              [data-persistent="true"] {
                contain: layout style paint;
                isolation: isolate;
              }
            }
          `;
          document.head.appendChild(style);
        }
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  return {
    isMobile: isMobileRef.current,
    applyMobileOptimizations: (element) => {
      if (isMobileRef.current && element) {
        element.style.contain = 'layout style paint';
        element.style.willChange = 'auto';
        element.style.transform = 'translateZ(0)';
      }
    }
  };
};

export default usePersistentMount;
